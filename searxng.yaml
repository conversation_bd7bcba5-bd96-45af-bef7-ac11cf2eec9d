apiVersion: apps/v1
kind: Deployment
metadata:
  name: searxng
  labels:
    app: searxng
spec:
  replicas: 1
  selector:
    matchLabels:
      app: searxng
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: searxng
    spec:
      containers:
        - name: searxng
          image: searxng/searxng
          imagePullPolicy: Always
          volumeMounts:
          - name: settings
            mountPath: /etc/searxng/settings.yml
            subPath: settings.yml
          envFrom:
            - configMapRef:
                name: searxng
      volumes:
        - name: settings
          configMap:
            name: settings.yml
            items:
              - key: settings.yml
                path: settings.yml
---
apiVersion: v1
kind: Service
metadata:
  name: searxng
  labels:
    app: searxng
    service: searxng
spec:
  selector:
    app: searxng
  ports:
  - name: http
    protocol: TCP
    port: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: searxng
spec:
  tls:
    - secretName: thespam-nl
      hosts:
        - searxng.pro.thespam.nl
  rules:
  - host: searxng.pro.thespam.nl
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: searxng
            port:
              number: 8080
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: searxng
data:
  SEARXNG_BASE_URL: "https://searxng.pro.thespam.nl/"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: settings.yml
data:
  settings.yml: |
    # see https://github.com/searxng/searxng/blob/master/searx/settings.yml
    use_default_settings: true
    general:
      enable_metrics: false
    search:
      default_lang: "auto"
      languages:
        - en
        - pt
      formats:
        - html
        - json
        - csv
        - rss
    server:
      secret_key: "object-hurricane-squealer"
      method: "GET"
    ui:
      results_on_new_tab: true
    engines:
    - name: wiby
      disabled: false
    - name: mojeek
      disabled: false
    - name: google
      disabled: true
