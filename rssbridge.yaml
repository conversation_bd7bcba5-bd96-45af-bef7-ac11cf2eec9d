apiVersion: v1
kind: ConfigMap
metadata:
  name: whitelist.txt
data:
  whitelist.txt: "*"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rssbridge
  name: rssbridge
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rssbridge
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: rssbridge
    spec:
      containers:
        - name: rssbridge
          image: rssbridge/rss-bridge
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          volumeMounts:
            - name: whitelist
              mountPath: /config/whitelist.txt
              subPath: whitelist.txt
      volumes:
        - name: whitelist
          configMap:
            name: whitelist.txt
            items:
              - key: whitelist.txt
                path: whitelist.txt
---
apiVersion: v1
kind: Service
metadata:
  name: rssbridge
  labels:
    app: rssbridge
    service: rssbridge
spec:
  selector:
    app: rssbridge
  ports:
  - protocol: TCP
    port: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rssbridge
spec:
  tls:
    - secretName: thespam-nl
      hosts:
        - rssbridge.pro.thespam.nl
  rules:
  - host: rssbridge.pro.thespam.nl
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: rssbridge
            port:
              number: 80
