# Coding Guidelines

## When Writing Code

- **Always evaluate if refactoring would add value**
- **Make the code easy to change**
- **When adding new code, ask yourself: does this make the code easier to change?**
- **Not all code needs refactoring** - If the code is already clean, expressive, and well-structured, commit and move on. Refactoring should improve the code - don't change things just for the sake of change.

## When Writing Tests

### Core Principles
- **Test behavior, not implementation** - Tests should verify expected behavior, treating implementation as a black box
- **Test through the public API exclusively** - Internals should be invisible to tests
- **Tests that examine internal implementation details are wasteful and should be avoided**
- **Mock external dependencies only**

### Test Structure
- **Use descriptive test names** that describe the expected behavior
- **Cover happy path + edge cases + errors**
- **Coverage targets**: 100% coverage should be expected at all times, but these tests must ALWAYS be based on business behaviour, not implementation details

### Documentation
- **Tests must document expected business behaviour**

## Overall Philosophy

The key is to write clean, testable, functional code that evolves through small, safe increments. Every change should be driven by a test that describes the desired behavior, and the implementation should be the simplest thing that makes that test pass. 

**When in doubt, favor simplicity and readability over cleverness.**