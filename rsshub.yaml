apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: rsshub
  name: rsshub
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rsshub
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: rsshub
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      containers:
        - name: rsshub
          image: diygod/rsshub
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: rsshub
---
apiVersion: v1
kind: Service
metadata:
  name: rsshub
  labels:
    app: rsshub
    service: rsshub
spec:
  selector:
    app: rsshub
  ports:
  - protocol: TCP
    port: 1200
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rsshub
spec:
  tls:
    - secretName: thespam-nl
      hosts:
        - rsshub.pro.thespam.nl
  rules:
  - host: rsshub.pro.thespam.nl
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: rsshub
            port:
              number: 1200
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: rsshub
data:
  NODE_ENV: "production"
  CACHE_TYPE: "memory"
  GITHUB_ACCESS_TOKEN: "****************************************"